// orderbook_dataset.rs
// Dataset loading and preparation for orderbook data

use candle_core::{<PERSON><PERSON>, Result, Tensor};
use polars::prelude::*;
use std::path::Path;
use candle_bert_time_series::batcher::TensorDataset;
use rayon::prelude::*;

/// Struct to hold orderbook data in CPU memory for efficient batching
#[derive(Clone)]
pub struct OrderbookDataset {
    pub data: Vec<Vec<f32>>, // [timestep][feature]
    pub num_features: usize,
    pub num_timesteps: usize,
}

impl OrderbookDataset {
    /// Create a tensor from a specific range of timesteps
    pub fn get_tensor_range(&self, start: usize, length: usize, device: &Device) -> Result<Tensor> {
        if start + length > self.num_timesteps {
            return Err(candle_core::Error::Msg(format!(
                "Range out of bounds: start={}, length={}, total_timesteps={}",
                start, length, self.num_timesteps
            )));
        }

        // For small ranges, use sequential processing to avoid overhead
        if length < 1000 {
            let mut tensor_data = Vec::with_capacity(length * self.num_features);
            for i in start..(start + length) {
                tensor_data.extend_from_slice(&self.data[i]);
            }
            Tensor::from_vec(tensor_data, &[length, self.num_features], device)
        } else {
            // For large ranges, use parallel processing
            let tensor_data: Vec<f32> = (start..(start + length))
                .into_par_iter()
                .flat_map(|i| self.data[i].clone())
                .collect();

            Tensor::from_vec(tensor_data, &[length, self.num_features], device)
        }
    }

    /// Get a single timestep as a tensor
    pub fn get_timestep_tensor(&self, timestep: usize, device: &Device) -> Result<Tensor> {
        if timestep >= self.num_timesteps {
            return Err(candle_core::Error::Msg(format!(
                "Timestep out of bounds: {} >= {}",
                timestep, self.num_timesteps
            )));
        }

        Tensor::from_vec(self.data[timestep].clone(), &[1, self.num_features], device)
    }

    /// Create a tensor from the entire dataset using parallel processing
    pub fn get_full_tensor(&self, device: &Device) -> Result<Tensor> {
        println!("Creating full tensor with parallel processing...");

        let tensor_data: Vec<f32> = self.data
            .par_iter()
            .flat_map(|row| row.iter().cloned())
            .collect();

        Tensor::from_vec(tensor_data, &[self.num_timesteps, self.num_features], device)
    }
}

impl TensorDataset for OrderbookDataset {
    fn get_tensor_range(&self, start: usize, length: usize, device: &Device) -> Result<Tensor> {
        self.get_tensor_range(start, length, device)
    }

    fn num_timesteps(&self) -> usize {
        self.num_timesteps
    }

    fn num_features(&self) -> usize {
        self.num_features
    }
}

/// Load and prepare orderbook data from multiple parquet files
/// Returns OrderbookDataset for efficient batching
pub fn load_and_prepare_orderbook_data(
    file_paths: &[&str],
) -> Result<OrderbookDataset> {
    println!("Loading orderbook data from {} files...", file_paths.len());
    
    // Load all parquet files in parallel
    println!("Loading files in parallel...");
    let all_dataframes: Result<Vec<_>> = file_paths
        .par_iter()
        .enumerate()
        .filter_map(|(i, path)| {
            if !Path::new(path).exists() {
                println!("Warning: File does not exist: {}", path);
                return None;
            }

            println!("Loading file {}/{}: {}", i + 1, file_paths.len(), path);

            let result = LazyFrame::scan_parquet(path, Default::default())
                .map_err(|e| candle_core::Error::Msg(format!("Failed to scan parquet file {}: {}", path, e)))
                .and_then(|lazy_frame| {
                    lazy_frame.collect()
                        .map_err(|e| candle_core::Error::Msg(format!("Failed to load parquet file {}: {}", path, e)))
                });

            match &result {
                Ok(df) => println!("  Loaded {} rows × {} columns", df.height(), df.width()),
                Err(e) => println!("  Error loading {}: {}", path, e),
            }

            Some(result)
        })
        .collect();

    let all_dataframes = all_dataframes?;
    
    if all_dataframes.is_empty() {
        return Err(candle_core::Error::Msg("No valid data files found".to_string()));
    }
    
    // Concatenate all dataframes
    println!("Concatenating {} dataframes...", all_dataframes.len());
    let mut combined_df = all_dataframes.into_iter().reduce(|mut acc, df| {
        acc.vstack_mut(&df).expect("Failed to concatenate dataframes");
        acc
    }).unwrap();
    
    println!("Combined dataset: {} rows × {} columns", combined_df.height(), combined_df.width());
    
    // Remove timestamp column if present
    let feature_columns: Vec<String> = combined_df.get_column_names()
        .iter()
        .filter(|name| !name.to_lowercase().contains("timestamp"))
        .map(|s| s.to_string())
        .collect();
    
    println!("Feature columns: {} (excluding timestamp)", feature_columns.len());
    
    // Select only feature columns
    combined_df = combined_df.select(&feature_columns)
        .map_err(|e| candle_core::Error::Msg(format!("Failed to select feature columns: {}", e)))?;
    
    // Extract data as CPU-resident vectors (no GPU tensor creation yet)
    let num_timesteps = combined_df.height();
    let num_features = combined_df.width();

    println!("Extracting data: {} timesteps × {} features", num_timesteps, num_features);

    // Extract data as row-major Vec<Vec<f32>> for efficient CPU storage
    // Pre-extract all columns as f32 arrays to avoid repeated column access
    println!("Pre-extracting columns for faster processing...");
    let column_arrays: Result<Vec<_>> = feature_columns
        .iter()
        .map(|col_name| {
            let column = combined_df.column(col_name)
                .map_err(|e| candle_core::Error::Msg(format!("Failed to get column {}: {}", col_name, e)))?;

            let f32_array = column
                .f32()
                .map_err(|e| candle_core::Error::Msg(format!("Failed to convert column {} to f32: {}", col_name, e)))?;

            Ok(f32_array)
        })
        .collect();

    let column_arrays = column_arrays?;

    println!("Extracting data using parallel processing...");

    // Use parallel processing to extract rows
    let data: Vec<Vec<f32>> = (0..num_timesteps)
        .into_par_iter()
        .map(|row_idx| {
            let mut row_data = Vec::with_capacity(num_features);

            for col_array in &column_arrays {
                let value = col_array.get(row_idx).unwrap_or(0.0);
                row_data.push(value);
            }

            row_data
        })
        .collect();

    let dataset = OrderbookDataset {
        data,
        num_features,
        num_timesteps,
    };

    println!("✅ Dataset created: {} timesteps × {} features (CPU memory)",
             dataset.num_timesteps, dataset.num_features);

    // Print some statistics using a sample tensor
    let sample_tensor = dataset.get_tensor_range(0, 1000.min(dataset.num_timesteps), &candle_core::Device::Cpu)?;
    print_data_statistics(&sample_tensor)?;

    Ok(dataset)
}

/// Print basic statistics about the loaded data
fn print_data_statistics(data: &Tensor) -> Result<()> {
    let shape = data.shape();
    println!("\n📊 Data Statistics:");
    println!("  Shape: {:?}", shape);
    
    // Calculate basic statistics
    let flattened = data.flatten_all()?;
    let mean = flattened.mean_all()?.to_scalar::<f32>()?;
    let std = flattened.var(0)?.sqrt()?.to_scalar::<f32>()?;
    let min_val = flattened.min(0)?.to_scalar::<f32>()?;
    let max_val = flattened.max(0)?.to_scalar::<f32>()?;
    
    println!("  Mean: {:.6}", mean);
    println!("  Std:  {:.6}", std);
    println!("  Min:  {:.6}", min_val);
    println!("  Max:  {:.6}", max_val);
    
    // Check for NaN or infinite values (simplified check)
    // Note: candle doesn't have isnan/isinf methods, so we'll skip this check for now
    // In a production system, you might want to implement custom NaN/Inf detection
    println!("  ℹ️  NaN/Inf checking skipped (not available in candle)");
    
    // Check data distribution
    let zero_mask = flattened.eq(&Tensor::zeros_like(&flattened)?)?;
    let zero_count = zero_mask.to_dtype(candle_core::DType::F32)?.sum_all()?.to_scalar::<f32>()?;
    let total_elements = flattened.elem_count() as f32;
    let zero_percentage = (zero_count / total_elements) * 100.0;
    
    println!("  Zero values: {:.2}% ({} out of {})", 
             zero_percentage, zero_count as usize, total_elements as usize);
    
    Ok(())
}

/// Load a single orderbook parquet file for inspection
pub fn inspect_orderbook_file(file_path: &str) -> Result<()> {
    println!("🔍 Inspecting orderbook file: {}", file_path);
    
    if !Path::new(file_path).exists() {
        return Err(candle_core::Error::Msg(format!("File does not exist: {}", file_path)));
    }
    
    let df = LazyFrame::scan_parquet(file_path, Default::default())
        .map_err(|e| candle_core::Error::Msg(format!("Failed to scan parquet file: {}", e)))?
        .limit(5) // Just look at first 5 rows
        .collect()
        .map_err(|e| candle_core::Error::Msg(format!("Failed to load parquet file: {}", e)))?;
    
    println!("📊 File structure:");
    println!("  Columns: {:?}", df.get_column_names());
    println!("  Shape: {:?}", df.shape());
    println!("  Data types: {:?}", df.dtypes());
    
    println!("\n📋 Sample data (first 5 rows):");
    println!("{}", df);
    
    // Check for expected orderbook structure
    let column_names = df.get_column_names();
    let has_timestamp = column_names.iter().any(|name| name.to_lowercase().contains("timestamp"));
    let bid_price_cols: Vec<_> = column_names.iter().filter(|name| name.contains("bid_price")).collect();
    let ask_price_cols: Vec<_> = column_names.iter().filter(|name| name.contains("ask_price")).collect();
    
    println!("\n🔍 Orderbook structure analysis:");
    println!("  Has timestamp: {}", has_timestamp);
    println!("  Bid price columns: {}", bid_price_cols.len());
    println!("  Ask price columns: {}", ask_price_cols.len());
    
    if bid_price_cols.len() == ask_price_cols.len() {
        println!("  ✅ Balanced bid/ask structure with {} levels", bid_price_cols.len());
    } else {
        println!("  ⚠️  Unbalanced bid/ask structure");
    }
    
    let expected_features = bid_price_cols.len() * 4; // bid_price, bid_qty, ask_price, ask_qty per level
    let actual_features = column_names.len() - if has_timestamp { 1 } else { 0 };
    
    println!("  Expected features: {} ({}×4)", expected_features, bid_price_cols.len());
    println!("  Actual features: {}", actual_features);
    
    if expected_features == actual_features {
        println!("  ✅ Feature count matches expected orderbook structure");
    } else {
        println!("  ⚠️  Feature count mismatch - check data format");
    }
    
    Ok(())
}

/// Validate that all files have consistent structure
pub fn validate_orderbook_files(file_paths: &[&str]) -> Result<()> {
    println!("🔍 Validating {} orderbook files...", file_paths.len());
    
    let mut reference_columns: Option<Vec<String>> = None;
    
    for (i, path) in file_paths.iter().enumerate() {
        if !Path::new(path).exists() {
            println!("⚠️  File {}: Does not exist - {}", i + 1, path);
            continue;
        }
        
        let df = LazyFrame::scan_parquet(path, Default::default())
            .map_err(|e| candle_core::Error::Msg(format!("Failed to scan file {}: {}", path, e)))?
            .limit(1) // Just check structure
            .collect()
            .map_err(|e| candle_core::Error::Msg(format!("Failed to load file {}: {}", path, e)))?;
        
        let columns: Vec<String> = df.get_column_names().iter().map(|s| s.to_string()).collect();
        let shape = df.shape();
        
        match &reference_columns {
            None => {
                reference_columns = Some(columns.clone());
                println!("✅ File {}: Reference structure set - {} columns", i + 1, shape.1);
            }
            Some(ref_cols) => {
                if columns == *ref_cols {
                    println!("✅ File {}: Structure matches reference", i + 1);
                } else {
                    println!("❌ File {}: Structure mismatch", i + 1);
                    println!("   Expected columns: {}", ref_cols.len());
                    println!("   Actual columns: {}", columns.len());
                    return Err(candle_core::Error::Msg(format!("File structure mismatch: {}", path)));
                }
            }
        }
    }
    
    println!("✅ All files have consistent structure");
    Ok(())
}
